import { useState, useEffect } from "react";
import { format } from "date-fns";
import { useTranslation } from "react-i18next";
import { useLanguage } from "@/contexts/LanguageContext";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Calendar as CalendarIcon,
  Users,
  Search,
  Plus,
  Minus,
  MapPin,
  Sparkles,
  ArrowRight,
  ArrowLeft,
} from "lucide-react";
import { cn } from "@/lib/utils";

const SearchForm = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [activeField, setActiveField] = useState<string | null>(null);
  const [checkIn, setCheckIn] = useState<Date>();
  const [checkOut, setCheckOut] = useState<Date>();
  const [guests, setGuests] = useState(1);
  const [isSearching, setIsSearching] = useState(false);
  const [focusedField, setFocusedField] = useState<string | null>(null);

  // Auto-set checkout date when checkin is selected
  useEffect(() => {
    if (checkIn && !checkOut) {
      const nextDay = new Date(checkIn);
      nextDay.setDate(nextDay.getDate() + 1);
      setCheckOut(nextDay);
    }
  }, [checkIn, checkOut]);

  const handleSearch = async () => {
    setIsSearching(true);
    // Simulate search delay
    await new Promise((resolve) => setTimeout(resolve, 1500));
    setIsSearching(false);
    // Add your search logic here
  };

  const getDaysDifference = () => {
    if (checkIn && checkOut) {
      const diffTime = Math.abs(checkOut.getTime() - checkIn.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      return diffDays;
    }
    return 0;
  };

  return (
    <div className="relative">
      {/* Background decoration */}
      <div className="absolute inset-0 bg-gradient-to-r from-blue-50 via-indigo-50 to-purple-50 rounded-3xl transform rotate-1 scale-105 opacity-30"></div>
      <div className="absolute inset-0 bg-gradient-to-l from-emerald-50 via-teal-50 to-cyan-50 rounded-3xl transform -rotate-1 scale-105 opacity-20"></div>

      <Card className="relative w-full max-w-6xl mx-auto bg-white/98 backdrop-blur-2xl shadow-2xl border border-white/20 rounded-3xl overflow-hidden">
        {/* Header with sparkle animation */}
        <div className="relative p-6 lg:p-8 border-b border-gray-100/50">
          <div className="flex items-center justify-center space-x-2">
            <Sparkles className="h-5 w-5 text-blue-500 animate-pulse" />
            <h2 className="text-xl font-bold text-gray-800 text-center">
              {t("search.findYourPerfectStay") || "ابحث عن إقامتك المثالية"}
            </h2>
            <Sparkles className="h-5 w-5 text-purple-500 animate-pulse" />
          </div>
        </div>

        <div className="p-6 lg:p-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 lg:gap-0">
            {/* Check-in Date */}
            <div
              className={cn(
                "relative group lg:border-r border-gray-200/30 last:border-r-0 p-6 transition-all duration-300 rounded-2xl lg:rounded-none",
                "hover:bg-gradient-to-br hover:from-blue-50/80 hover:to-indigo-50/80 hover:shadow-lg hover:scale-[1.02]",
                focusedField === "checkin" &&
                  "bg-gradient-to-br from-blue-50/80 to-indigo-50/80 shadow-lg scale-[1.02] ring-2 ring-blue-200/50"
              )}
              onMouseEnter={() => setFocusedField("checkin")}
              onMouseLeave={() => setFocusedField(null)}
            >
              <div className="flex items-center space-x-2 mb-3">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                <div className="text-xs font-bold text-gray-600 uppercase tracking-wide">
                  {t("search.checkIn")}
                </div>
              </div>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="ghost"
                    className={cn(
                      "w-full h-auto p-0 justify-start text-left font-normal hover:bg-transparent group-hover:scale-105 transition-all duration-300",
                      !checkIn && "text-gray-500"
                    )}
                  >
                    <div className="flex items-center space-x-3">
                      <div
                        className={cn(
                          "p-3 rounded-xl bg-gradient-to-br from-blue-100 to-blue-200 group-hover:from-blue-200 group-hover:to-blue-300 transition-all duration-300 shadow-sm",
                          isRTL && "ml-3",
                          checkIn && "from-blue-500 to-blue-600 text-white"
                        )}
                      >
                        <CalendarIcon
                          className={cn(
                            "h-5 w-5",
                            checkIn ? "text-white" : "text-blue-600"
                          )}
                        />
                      </div>
                      <div className="flex-1">
                        <div className="text-lg font-bold text-gray-900">
                          {checkIn
                            ? format(checkIn, "MMM dd")
                            : t("search.selectDate")}
                        </div>
                        <div className="text-sm text-gray-500">
                          {checkIn ? format(checkIn, "yyyy") : "تاريخ الوصول"}
                        </div>
                      </div>
                      {checkIn && (
                        <div className="text-right">
                          <div className="text-xs text-blue-600 font-medium">
                            محدد
                          </div>
                          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        </div>
                      )}
                    </div>
                  </Button>
                </PopoverTrigger>
                <PopoverContent
                  className="w-auto p-0 bg-white/95 backdrop-blur-xl border border-gray-200/50 shadow-2xl rounded-2xl z-50 overflow-hidden"
                  align="start"
                >
                  <div className="p-4 border-b border-gray-100">
                    <h3 className="font-semibold text-gray-800">
                      اختر تاريخ الوصول
                    </h3>
                  </div>
                  <Calendar
                    mode="single"
                    selected={checkIn}
                    onSelect={setCheckIn}
                    disabled={(date) => date < new Date()}
                    initialFocus
                    className="pointer-events-auto"
                  />
                </PopoverContent>
              </Popover>
            </div>

            {/* Check-out Date */}
            <div
              className={cn(
                "relative group lg:border-r border-gray-200/30 last:border-r-0 p-6 transition-all duration-300 rounded-2xl lg:rounded-none",
                "hover:bg-gradient-to-br hover:from-purple-50/80 hover:to-pink-50/80 hover:shadow-lg hover:scale-[1.02]",
                focusedField === "checkout" &&
                  "bg-gradient-to-br from-purple-50/80 to-pink-50/80 shadow-lg scale-[1.02] ring-2 ring-purple-200/50"
              )}
              onMouseEnter={() => setFocusedField("checkout")}
              onMouseLeave={() => setFocusedField(null)}
            >
              <div className="flex items-center space-x-2 mb-3">
                <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></div>
                <div className="text-xs font-bold text-gray-600 uppercase tracking-wide">
                  {t("search.checkOut")}
                </div>
              </div>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="ghost"
                    className={cn(
                      "w-full h-auto p-0 justify-start text-left font-normal hover:bg-transparent group-hover:scale-105 transition-all duration-300",
                      !checkOut && "text-gray-500"
                    )}
                  >
                    <div className="flex items-center space-x-3">
                      <div
                        className={cn(
                          "p-3 rounded-xl bg-gradient-to-br from-purple-100 to-purple-200 group-hover:from-purple-200 group-hover:to-purple-300 transition-all duration-300 shadow-sm",
                          isRTL && "ml-3",
                          checkOut && "from-purple-500 to-purple-600 text-white"
                        )}
                      >
                        <CalendarIcon
                          className={cn(
                            "h-5 w-5",
                            checkOut ? "text-white" : "text-purple-600"
                          )}
                        />
                      </div>
                      <div className="flex-1">
                        <div className="text-lg font-bold text-gray-900">
                          {checkOut
                            ? format(checkOut, "MMM dd")
                            : t("search.selectDate")}
                        </div>
                        <div className="text-sm text-gray-500">
                          {checkOut
                            ? format(checkOut, "yyyy")
                            : "تاريخ المغادرة"}
                        </div>
                      </div>
                      {checkOut && (
                        <div className="text-right">
                          <div className="text-xs text-purple-600 font-medium">
                            محدد
                          </div>
                          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        </div>
                      )}
                    </div>
                  </Button>
                </PopoverTrigger>
                <PopoverContent
                  className="w-auto p-0 bg-white/95 backdrop-blur-xl border border-gray-200/50 shadow-2xl rounded-2xl z-50 overflow-hidden"
                  align="start"
                >
                  <div className="p-4 border-b border-gray-100">
                    <h3 className="font-semibold text-gray-800">
                      اختر تاريخ المغادرة
                    </h3>
                    {checkIn && (
                      <p className="text-sm text-gray-600 mt-1">
                        بعد {format(checkIn, "MMM dd, yyyy")}
                      </p>
                    )}
                  </div>
                  <Calendar
                    mode="single"
                    selected={checkOut}
                    onSelect={setCheckOut}
                    disabled={(date) =>
                      date < new Date() || (checkIn && date <= checkIn)
                    }
                    initialFocus
                    className="pointer-events-auto"
                  />
                </PopoverContent>
              </Popover>

              {/* Duration indicator */}
              {checkIn && checkOut && (
                <div className="absolute -top-2 -right-2 bg-gradient-to-r from-green-500 to-emerald-500 text-white text-xs font-bold px-2 py-1 rounded-full shadow-lg">
                  {getDaysDifference()}{" "}
                  {getDaysDifference() === 1 ? "ليلة" : "ليالي"}
                </div>
              )}
            </div>

            {/* Guests */}
            <div
              className={cn(
                "relative group lg:border-r border-gray-200/30 last:border-r-0 p-6 transition-all duration-300 rounded-2xl lg:rounded-none",
                "hover:bg-gradient-to-br hover:from-emerald-50/80 hover:to-teal-50/80 hover:shadow-lg hover:scale-[1.02]",
                focusedField === "guests" &&
                  "bg-gradient-to-br from-emerald-50/80 to-teal-50/80 shadow-lg scale-[1.02] ring-2 ring-emerald-200/50"
              )}
              onMouseEnter={() => setFocusedField("guests")}
              onMouseLeave={() => setFocusedField(null)}
            >
              <div className="flex items-center space-x-2 mb-3">
                <div className="w-2 h-2 bg-emerald-500 rounded-full animate-pulse"></div>
                <div className="text-xs font-bold text-gray-600 uppercase tracking-wide">
                  {t("search.guests")}
                </div>
              </div>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="ghost"
                    className="w-full h-auto p-0 justify-start hover:bg-transparent group-hover:scale-105 transition-all duration-300"
                  >
                    <div className="flex items-center space-x-3">
                      <div
                        className={cn(
                          "p-3 rounded-xl bg-gradient-to-br from-emerald-100 to-emerald-200 group-hover:from-emerald-200 group-hover:to-emerald-300 transition-all duration-300 shadow-sm",
                          isRTL && "ml-3"
                        )}
                      >
                        <Users className="h-5 w-5 text-emerald-600" />
                      </div>
                      <div className="flex-1">
                        <div className="text-lg font-bold text-gray-900">
                          {guests}{" "}
                          {guests === 1
                            ? t("search.guest") || "نزيل"
                            : t("search.guestsPlural") || "نزلاء"}
                        </div>
                        <div className="text-sm text-gray-500">
                          اختر عدد النزلاء
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-xs text-emerald-600 font-medium">
                          {guests > 1 ? "متعدد" : "فردي"}
                        </div>
                        <div className="flex space-x-1">
                          {Array.from({ length: Math.min(guests, 4) }).map(
                            (_, i) => (
                              <div
                                key={i}
                                className="w-1.5 h-1.5 bg-emerald-500 rounded-full"
                              ></div>
                            )
                          )}
                          {guests > 4 && (
                            <div className="text-xs text-emerald-600">+</div>
                          )}
                        </div>
                      </div>
                    </div>
                  </Button>
                </PopoverTrigger>
                <PopoverContent
                  className="w-80 p-6 bg-white/95 backdrop-blur-xl border border-gray-200/50 shadow-2xl rounded-2xl z-50"
                  align="start"
                >
                  <div className="space-y-6">
                    <div className="text-center">
                      <h3 className="text-lg font-bold text-gray-800 mb-2">
                        عدد النزلاء
                      </h3>
                      <p className="text-sm text-gray-600">
                        اختر العدد المناسب لإقامتك
                      </p>
                    </div>

                    <div className="flex items-center justify-between bg-gray-50 rounded-2xl p-4">
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-emerald-100 rounded-xl">
                          <Users className="h-5 w-5 text-emerald-600" />
                        </div>
                        <div>
                          <div className="font-semibold text-gray-900">
                            النزلاء
                          </div>
                          <div className="text-sm text-gray-500">
                            الحد الأقصى 10
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center space-x-4">
                        <Button
                          variant="outline"
                          size="icon"
                          className="h-12 w-12 rounded-full border-2 border-emerald-200 hover:bg-emerald-50 hover:border-emerald-300 transition-all duration-200"
                          onClick={() => setGuests(Math.max(1, guests - 1))}
                          disabled={guests <= 1}
                        >
                          <Minus className="h-5 w-5 text-emerald-600" />
                        </Button>

                        <div className="w-16 text-center">
                          <div className="text-2xl font-bold text-gray-900">
                            {guests}
                          </div>
                          <div className="text-xs text-gray-500">
                            {guests === 1 ? "نزيل" : "نزلاء"}
                          </div>
                        </div>

                        <Button
                          variant="outline"
                          size="icon"
                          className="h-12 w-12 rounded-full border-2 border-emerald-200 hover:bg-emerald-50 hover:border-emerald-300 transition-all duration-200"
                          onClick={() => setGuests(Math.min(10, guests + 1))}
                          disabled={guests >= 10}
                        >
                          <Plus className="h-5 w-5 text-emerald-600" />
                        </Button>
                      </div>
                    </div>

                    {/* Quick select buttons */}
                    <div className="grid grid-cols-3 gap-2">
                      {[1, 2, 4].map((num) => (
                        <Button
                          key={num}
                          variant={guests === num ? "default" : "outline"}
                          size="sm"
                          className={cn(
                            "rounded-xl transition-all duration-200",
                            guests === num
                              ? "bg-emerald-500 hover:bg-emerald-600 text-white"
                              : "border-emerald-200 hover:bg-emerald-50"
                          )}
                          onClick={() => setGuests(num)}
                        >
                          {num} {num === 1 ? "نزيل" : "نزلاء"}
                        </Button>
                      ))}
                    </div>
                  </div>
                </PopoverContent>
              </Popover>
            </div>

            {/* Search Button */}
            <div
              className={cn(
                "relative p-6 flex items-center transition-all duration-300",
                focusedField === "search" && "scale-[1.02]"
              )}
              onMouseEnter={() => setFocusedField("search")}
              onMouseLeave={() => setFocusedField(null)}
            >
              <Button
                onClick={handleSearch}
                disabled={isSearching || !checkIn || !checkOut}
                className={cn(
                  "w-full h-16 bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 hover:from-blue-700 hover:via-indigo-700 hover:to-purple-700",
                  "text-white shadow-2xl transition-all duration-500 hover:shadow-3xl hover:scale-105 font-bold text-xl rounded-3xl border-0",
                  "disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100",
                  "relative overflow-hidden group"
                )}
                size="lg"
              >
                {/* Animated background */}
                <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

                <div className="relative flex items-center justify-center space-x-3">
                  {isSearching ? (
                    <>
                      <div className="animate-spin rounded-full h-6 w-6 border-2 border-white border-t-transparent"></div>
                      <span>جاري البحث...</span>
                    </>
                  ) : (
                    <>
                      <Search
                        className={cn(
                          "h-7 w-7 transition-transform duration-300 group-hover:scale-110",
                          isRTL ? "ml-3" : "mr-3"
                        )}
                      />
                      <span className="transition-all duration-300 group-hover:tracking-wide">
                        {t("search.search") || "ابحث الآن"}
                      </span>
                      <div
                        className={cn(
                          "transition-transform duration-300 group-hover:translate-x-1",
                          isRTL ? "rotate-180" : ""
                        )}
                      >
                        {isRTL ? (
                          <ArrowLeft className="h-5 w-5" />
                        ) : (
                          <ArrowRight className="h-5 w-5" />
                        )}
                      </div>
                    </>
                  )}
                </div>
              </Button>
            </div>
          </div>
        </div>

        {/* Summary bar */}
        {(checkIn || checkOut || guests > 1) && (
          <div className="px-6 lg:px-8 pb-6">
            <div className="bg-gradient-to-r from-gray-50 to-blue-50/30 rounded-2xl p-4 border border-gray-100">
              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center space-x-4">
                  {checkIn && (
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      <span className="text-gray-600">
                        من: {format(checkIn, "MMM dd")}
                      </span>
                    </div>
                  )}
                  {checkOut && (
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                      <span className="text-gray-600">
                        إلى: {format(checkOut, "MMM dd")}
                      </span>
                    </div>
                  )}
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-emerald-500 rounded-full"></div>
                    <span className="text-gray-600">
                      {guests} {guests === 1 ? "نزيل" : "نزلاء"}
                    </span>
                  </div>
                </div>
                {checkIn && checkOut && (
                  <div className="text-blue-600 font-semibold">
                    {getDaysDifference()}{" "}
                    {getDaysDifference() === 1 ? "ليلة" : "ليالي"}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </Card>
    </div>
  );
};

export default SearchForm;
